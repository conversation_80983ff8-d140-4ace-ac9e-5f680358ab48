package com.wexl.erp.fees.service;

import static com.wexl.retail.commons.util.DateTimeUtil.convertIso8601ToEpoch;

import com.wexl.erp.fees.dto.FeeDto;
import com.wexl.erp.fees.model.FeeHead;
import com.wexl.erp.fees.repository.FeeHeadRepository;
import com.wexl.retail.commons.util.DateTimeUtil;
import com.wexl.retail.model.Student;
import com.wexl.retail.repository.StudentRepository;
import com.wexl.retail.section.domain.Section;
import com.wexl.retail.section.repository.SectionRepository;
import com.wexl.retail.telegram.service.UserService;
import com.wexl.retail.util.CsvUtils;
import jakarta.servlet.http.HttpServletResponse;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
@Slf4j
public class FeeDueReportService {

  private final FeeHeadRepository feeHeadRepository;
  private final StudentRepository studentRepository;
  private final SectionRepository sectionRepository;
  private final UserService userService;
  private final DateTimeUtil dateTimeUtil;

  private static final List<String> STANDARD_COLUMNS = List.of(
      "STUDENT NAME", "Admission Number", "Roll No.", "CLASS", "Student status",
      "Father Name", "Father Mobile", "Father Email", "Mother Name",
      "Mother Mobile", "Mother EMail", "DND status", "Date of Adm#",
      "Student Category", "Hostel Category");

  private static final List<String> SUMMARY_COLUMNS = List.of(
      "Total Fee Assigned", "Concession Amount", "Total Paid", "Total Due", "Fee Remark");

  private static final List<String> TRANSPORT_MONTHS = List.of(
      "APR", "MAY", "JUN", "JUL", "AUG", "SEP", "OCT", "NOV", "DEC", "JAN", "FEB", "MAR");

  private static final List<String> TUITION_TERMS = List.of(
      "Term 1", "Term 2", "Term 3", "Term 4");

  private static final List<String> FEE_TYPE_ORDER = List.of(
      "Admission Fee", "Tuition Fee", "Transport Fee",
      "Tuition Fee - LateFee", "Transport Fee - LateFee",
      "Delete", "EXTENDED DAY CARE- LateFee"
  );

  private static final Map<String, List<String>> PREDEFINED_FEE_STRUCTURE = Map.of(
      "Admission Fee", List.of("Admission Fee"),
      "Tuition Fee", TUITION_TERMS,
      "Transport Fee", TRANSPORT_MONTHS,
      "Tuition Fee - LateFee", TUITION_TERMS,
      "Transport Fee - LateFee", TRANSPORT_MONTHS,
      "Delete", List.of("Extended Day Care"),
      "EXTENDED DAY CARE- LateFee", List.of("Extended Day Care")
  );

  public void generateFeeDueReportCsv(
      String orgSlug, FeeDto.FeeDueReportRequest request, HttpServletResponse response) {

    List<FeeDto.FeeDueReportResponse> reportData = generateFeeDueReport(orgSlug, request);
    String reportType = request.reportType() != null ? request.reportType() : "total_due";
    generateCsvResponse(reportData, response, reportType, request);
  }

  public List<FeeDto.FeeDueReportResponse> generateFeeDueReport(
      String orgSlug, FeeDto.FeeDueReportRequest request) {

    List<Student> students = getStudentsBySectionUuids(request);
    List<FeeHead> feeHeads = getFeeHeadsByReportType(orgSlug, students, request);

    Map<Student, List<FeeHead>> feeHeadsByStudent =
        feeHeads.stream().collect(Collectors.groupingBy(FeeHead::getStudent));

    List<FeeDto.FeeDueReportResponse> reportData = students.stream()
        .map(
            student -> {
              Double discount = calculateDiscount(student);
              return buildFeeDueReportResponse(discount, student, feeHeadsByStudent.get(student));
            })
        .filter(Objects::nonNull)
        .collect(Collectors.toList());

    reportData = filterStudentsWithDueFees(reportData);
    reportData = filterReportDataByFeeGroupTypes(reportData, request.feeGroupTypes());

    return reportData;
  }

  private Double calculateDiscount(Student student) {
    var feeHeadList = feeHeadRepository.findAllByStudent(student);
    return (feeHeadList == null || feeHeadList.isEmpty())
        ? 0.0
        : feeHeadList.stream()
            .map(FeeHead::getDiscountAmount)
            .filter(Objects::nonNull)
            .mapToDouble(Double::doubleValue)
            .sum();
  }

  public List<Student> getStudentsBySectionUuids(FeeDto.FeeDueReportRequest request) {
    List<Section> sections =
        sectionRepository.findAllByUuidIn(
            request.sectionUuids().stream().map(UUID::fromString).toList());
    List<Student> students = new ArrayList<>();
    for (Section section : sections) {
      List<Student> sectionStudents = studentRepository.getStudentsBySection(section);
      students.addAll(sectionStudents);
    }
    return students;
  }

  @Async
  private List<FeeHead> getFeeHeadsByReportType(
      String orgSlug, List<Student> students, FeeDto.FeeDueReportRequest request) {

    List<Long> studentIds = students.stream().map(Student::getId).collect(Collectors.toList());

    String reportType =
        request.reportType() != null ? request.reportType().toLowerCase() : "total_due";
    return switch (reportType) {
      case "past_due" ->
          feeHeadRepository.findPastDueFeeDetails(
              orgSlug,
              studentIds,
              dateTimeUtil.convertEpochToIso8601(request.fromDate()),
              dateTimeUtil.convertEpochToIso8601(request.toDate()));
      case "total_due" ->
          feeHeadRepository.findTotalDueFeeDetails(
              orgSlug,
              studentIds,
              dateTimeUtil.convertEpochToIso8601(request.fromDate()),
              dateTimeUtil.convertEpochToIso8601(request.toDate()));
      default -> Collections.emptyList();
    };
  }

  @Async
  private FeeDto.FeeDueReportResponse buildFeeDueReportResponse(
      Double discount, Student student, List<FeeHead> feeHeads) {
    if (feeHeads == null || feeHeads.isEmpty()) {
      return null;
    }

    List<FeeDto.FeeDetailResponse> feeDetails =
        feeHeads.stream().map(this::buildFeeDetailResponse).collect(Collectors.toList());

    Double totalDueAmount =
        feeHeads.stream()
            .mapToDouble(
                feeHead -> feeHead.getBalanceAmount() != null ? feeHead.getBalanceAmount() : 0.0)
            .sum();

    return FeeDto.FeeDueReportResponse.builder()
        .studentName(userService.getNameByUserInfo(student.getUserInfo()))
        .admissionNumber(student.getUserInfo().getUserName())
        .rollNumber(student.getClassRollNumber())
        .sectionName(student.getSection() != null ? student.getSection().getName() : "")
        .dateOfAdmission(
            student.getCreatedAt() != null
                ? student
                    .getCreatedAt()
                    .toLocalDateTime()
                    .format(DateTimeFormatter.ofPattern("dd-MM-yyyy"))
                : "")
        .feeDetails(feeDetails)
        .discountAmount(discount)
        .totalDueAmount(totalDueAmount)
        .build();
  }

  private FeeDto.FeeDetailResponse buildFeeDetailResponse(FeeHead feeHead) {
    return FeeDto.FeeDetailResponse.builder()
        .feeTypeName(feeHead.getFeeType().getDescription())
        .feeGroupName(feeHead.getFeeMaster().getFeeGroup().getName())
        .month(
            feeHead.getDueDate() != null
                ? feeHead.getDueDate().format(DateTimeFormatter.ofPattern("MMM")).toUpperCase()
                : "")
        .amount(feeHead.getAmount())
        .paidAmount(feeHead.getPaidAmount() != null ? feeHead.getPaidAmount() : 0.0)
        .balanceAmount(feeHead.getBalanceAmount() != null ? feeHead.getBalanceAmount() : 0.0)
        .dueDate(convertIso8601ToEpoch(feeHead.getDueDate()))
        .status(feeHead.getStatus())
        .build();
  }

  private List<FeeDto.FeeDueReportResponse> filterReportDataByFeeGroupTypes(
      List<FeeDto.FeeDueReportResponse> reportData, List<String> feeGroupTypeFilter) {

    if (feeGroupTypeFilter == null || feeGroupTypeFilter.isEmpty()) {
      return reportData;
    }

    return reportData.stream()
        .map(report -> {
          List<FeeDto.FeeDetailResponse> filteredFeeDetails = report.feeDetails().stream()
              .filter(detail -> feeGroupTypeFilter.contains(detail.feeGroupName()))
              .collect(Collectors.toList());

          Double filteredTotalDue = filteredFeeDetails.stream()
              .mapToDouble(detail -> detail.balanceAmount() != null ? detail.balanceAmount() : 0.0)
              .sum();

          return FeeDto.FeeDueReportResponse.builder()
              .studentName(report.studentName())
              .admissionNumber(report.admissionNumber())
              .rollNumber(report.rollNumber())
              .sectionName(report.sectionName())
              .dateOfAdmission(report.dateOfAdmission())
              .feeDetails(filteredFeeDetails)
              .discountAmount(report.discountAmount())
              .totalDueAmount(filteredTotalDue)
              .build();
        })
        .filter(report -> !report.feeDetails().isEmpty())
        .collect(Collectors.toList());
  }

  private List<FeeDto.FeeDueReportResponse> filterStudentsWithDueFees(
      List<FeeDto.FeeDueReportResponse> reportData) {

    return reportData.stream()
        .filter(report -> {
          boolean hasDueFees = report.feeDetails().stream()
              .anyMatch(detail -> detail.balanceAmount() != null && detail.balanceAmount() > 0);

          return hasDueFees;
        })
        .collect(Collectors.toList());
  }

  private void generateCsvResponse(
      List<FeeDto.FeeDueReportResponse> reportData,
      HttpServletResponse response,
      String reportType,
      FeeDto.FeeDueReportRequest request) {

    String fileName = getFileName(reportType);
    response.setContentType("text/csv");
    response.setHeader("Content-Disposition", "attachment; filename=" + fileName);

    List<List<String>> csvData = buildEnhancedCsvBody(reportData, request.feeGroupTypes());

    if (csvData.isEmpty()) {
      CsvUtils.generateCsv(new String[0], List.of(), response);
      return;
    }

    List<String> mainHeaders = csvData.remove(0);
    List<String> subHeaders = csvData.remove(0);

    csvData.add(0, subHeaders);

    CsvUtils.generateCsv(mainHeaders.toArray(new String[0]), csvData, response);
  }

  private String getFileName(String reportType) {
    String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"));
    return switch (reportType) {
      case "past_due" -> "past_due_report_" + timestamp + ".csv";
      case "total_due" -> "total_due_report_" + timestamp + ".csv";
      default -> "fee_due_report_" + timestamp + ".csv";
    };
  }

  private List<List<String>> buildEnhancedCsvBody(
      List<FeeDto.FeeDueReportResponse> reportData, List<String> feeGroupTypeFilter) {

    List<List<String>> csvData = new ArrayList<>();

    List<String> mainHeaders = new ArrayList<>();
    mainHeaders.addAll(STANDARD_COLUMNS);

    for (String feeType : FEE_TYPE_ORDER) {
      List<String> subHeaders = PREDEFINED_FEE_STRUCTURE.get(feeType);
      if (subHeaders != null) {
        for (int i = 0; i < subHeaders.size(); i++) {
          mainHeaders.add(feeType);
        }
      }
    }

    mainHeaders.addAll(SUMMARY_COLUMNS);
    csvData.add(mainHeaders);

    List<String> subHeaders = new ArrayList<>();

    for (int i = 0; i < STANDARD_COLUMNS.size(); i++) {
      subHeaders.add("");
    }

    for (String feeType : FEE_TYPE_ORDER) {
      List<String> feeSubHeaders = PREDEFINED_FEE_STRUCTURE.get(feeType);
      if (feeSubHeaders != null) {
        subHeaders.addAll(feeSubHeaders);
      }
    }

    for (int i = 0; i < SUMMARY_COLUMNS.size(); i++) {
      subHeaders.add("");
    }

    csvData.add(subHeaders);

    for (FeeDto.FeeDueReportResponse report : reportData) {
      List<String> row = new ArrayList<>();

      row.add(report.studentName() != null ? report.studentName() : "");
      row.add(report.admissionNumber() != null ? report.admissionNumber() : "");
      row.add(report.rollNumber() != null ? report.rollNumber() : "");
      row.add(report.sectionName() != null ? report.sectionName() : "");
      row.add("");
      row.add("");
      row.add("");
      row.add("");
      row.add("");
      row.add("");
      row.add("");
      row.add("");
      row.add(report.dateOfAdmission() != null ? report.dateOfAdmission() : "");
      row.add("");
      row.add("");

      Map<String, Double> feeDataMap = new HashMap<>();
      for (FeeDto.FeeDetailResponse detail : report.feeDetails()) {
        String period = (detail.month() != null && !detail.month().isEmpty())
            ? detail.month() : detail.feeTypeName();
        String columnKey = detail.feeTypeName() + "_" + period;
        feeDataMap.merge(columnKey,
            detail.balanceAmount() != null ? detail.balanceAmount() : 0.0,
            Double::sum);
      }

      for (String feeType : FEE_TYPE_ORDER) {
        List<String> feeSubHeaders = PREDEFINED_FEE_STRUCTURE.get(feeType);
        if (feeSubHeaders != null) {
          for (String subHeader : feeSubHeaders) {
            String columnKey = feeType + "_" + subHeader;
            Double value = feeDataMap.getOrDefault(columnKey, 0.0);
            row.add(String.format("%.0f", value));
          }
        }
      }

      Double totalAssigned = report.feeDetails().stream()
          .mapToDouble(detail -> detail.amount() != null ? detail.amount() : 0.0)
          .sum();
      Double totalPaid = report.feeDetails().stream()
          .mapToDouble(detail -> detail.paidAmount() != null ? detail.paidAmount() : 0.0)
          .sum();

      row.add(String.format("%.0f", totalAssigned));
      row.add(String.format("%.0f", report.discountAmount() != null ? report.discountAmount() : 0.0));
      row.add(String.format("%.0f", totalPaid));
      row.add(String.format("%.0f", report.totalDueAmount() != null ? report.totalDueAmount() : 0.0));
      row.add("");

      csvData.add(row);
    }

    return csvData;
  }
}
